def analyze_tweet(tweet, positive_words, negative_words):
    pos_count = 0
    neg_count = 0

    words = tweet.lower().split() #Split tweet into lowercase words

    for word in words:
        if word in positive_words:
            pos_count += 1
         elif word in negative_words:
            neg_count += 1

    if pos_count > neg_count:
        return "Positive"
    elif neg_cunt > pos_count:
        return "Negative"
    else:
        return "Neutral"

    #Example Usage
    positive_words = ["happy", "good", "love", "sunny"]
    negative_words = ["sad", "bad", "hate", "angry"]
    tweet = "I love the sunny weather, but I hate the traffic"

    print(analyze_tweet(tweet, positive_words, negative_words))